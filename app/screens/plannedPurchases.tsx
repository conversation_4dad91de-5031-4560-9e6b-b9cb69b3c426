import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet, ScrollView, Alert, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import i18n from '../i18n';
import { useSettings } from '../context/SettingsContext';
import { getPlannedPurchases, addPlannedPurchase, updatePlannedPurchase, deletePlannedPurchase, PlannedPurchase } from '../constants/Storage';

const PlannedPurchases = () => {
    const [items, setItems] = useState<PlannedPurchase[]>([]);
    const [showAddModal, setShowAddModal] = useState(false);
    const [editingItem, setEditingItem] = useState<PlannedPurchase | null>(null);
    const [itemName, setItemName] = useState('');
    const [targetPrice, setTargetPrice] = useState('');
    const [currentPrice, setCurrentPrice] = useState('');
    const [note, setNote] = useState('');
    const [priority, setPriority] = useState(1);

    const { currency } = useSettings();

    useEffect(() => {
        loadItems();
    }, []);

    const loadItems = async () => {
        try {
            const data = await getPlannedPurchases();
            setItems(data);
        } catch (error) {
            console.error('Failed to load planned purchases:', error);
        }
    };

    const handleAddItem = async () => {
        if (!itemName.trim()) {
            Alert.alert(i18n.t('common.error'), i18n.t('plannedPurchases.itemNameRequired'));
            return;
        }

        if (!targetPrice.trim() || isNaN(Number(targetPrice))) {
            Alert.alert(i18n.t('common.error'), i18n.t('plannedPurchases.targetPriceRequired'));
            return;
        }

        try {
            if (editingItem) {
                // 更新现有商品
                await updatePlannedPurchase(editingItem.id, {
                    name: itemName.trim(),
                    target_price: Number(targetPrice),
                    current_price: currentPrice ? Number(currentPrice) : undefined,
                    note: note.trim(),
                    priority: priority,
                });
            } else {
                // 添加新商品
                await addPlannedPurchase({
                    name: itemName.trim(),
                    target_price: Number(targetPrice),
                    current_price: currentPrice ? Number(currentPrice) : undefined,
                    note: note.trim(),
                    priority: priority,
                });
            }

            resetForm();
            setShowAddModal(false);
            loadItems();
        } catch (error) {
            console.error('Failed to save item:', error);
            Alert.alert(i18n.t('common.error'), editingItem ? i18n.t('plannedPurchases.updateItemFailed') : i18n.t('plannedPurchases.addItemFailed'));
        }
    };

    const handleEditItem = (item: PlannedPurchase) => {
        setEditingItem(item);
        setItemName(item.name);
        setTargetPrice(item.target_price.toString());
        setCurrentPrice(item.current_price ? item.current_price.toString() : '');
        setNote(item.note || '');
        setPriority(item.priority);
        setShowAddModal(true);
    };

    const handleDeleteItem = async (itemId: number) => {
        Alert.alert(
            i18n.t('plannedPurchases.confirmDelete'),
            i18n.t('plannedPurchases.confirmDeleteMessage'),
            [
                { text: i18n.t('common.cancel'), style: 'cancel' },
                {
                    text: i18n.t('common.delete'),
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await deletePlannedPurchase(itemId);
                            loadItems();
                        } catch (error) {
                            console.error('Failed to delete item:', error);
                            Alert.alert(i18n.t('common.error'), i18n.t('plannedPurchases.deleteItemFailed'));
                        }
                    }
                }
            ]
        );
    };

    const handleToggleCompleted = async (item: PlannedPurchase) => {
        try {
            await updatePlannedPurchase(item.id, {
                is_completed: !item.is_completed
            });
            loadItems();
        } catch (error) {
            console.error('Failed to toggle completion:', error);
        }
    };

    const resetForm = () => {
        setItemName('');
        setTargetPrice('');
        setCurrentPrice('');
        setNote('');
        setPriority(1);
        setEditingItem(null);
    };

    const getPriorityText = (priority: number) => {
        switch (priority) {
            case 3: return i18n.t('plannedPurchases.priorityHigh');
            case 2: return i18n.t('plannedPurchases.priorityMedium');
            default: return i18n.t('plannedPurchases.priorityLow');
        }
    };

    const getPriorityColor = (priority: number) => {
        switch (priority) {
            case 3: return '#dc4446';
            case 2: return '#FF9800';
            default: return '#4CAF50';
        }
    };

    const renderItem = (item: PlannedPurchase) => (
        <View key={item.id} style={[styles.itemCard, item.is_completed && styles.completedItem]}>
            <View style={styles.itemHeader}>
                <View style={styles.itemInfo}>
                    <Text style={[styles.itemName, item.is_completed && styles.completedText]}>{item.name}</Text>
                    <View style={styles.priceRow}>
                        <Text style={styles.priceLabel}>{i18n.t('plannedPurchases.targetPrice')}: </Text>
                        <Text style={styles.targetPrice}>{currency}{item.target_price.toFixed(2)}</Text>
                        {item.current_price && (
                            <>
                                <Text style={styles.priceLabel}> | {i18n.t('plannedPurchases.currentPrice')}: </Text>
                                <Text style={[styles.currentPrice, item.current_price <= item.target_price && styles.goodPrice]}>
                                    {currency}{item.current_price.toFixed(2)}
                                </Text>
                            </>
                        )}
                    </View>
                    {item.note && <Text style={styles.itemNote}>{item.note}</Text>}
                    <View style={styles.itemMeta}>
                        <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(item.priority) }]}>
                            <Text style={styles.priorityText}>{getPriorityText(item.priority)}</Text>
                        </View>
                        <Text style={styles.statusText}>
                            {item.is_completed ? i18n.t('plannedPurchases.completed') : i18n.t('plannedPurchases.pending')}
                        </Text>
                    </View>
                </View>
                <View style={styles.itemActions}>
                    <TouchableOpacity
                        style={[styles.actionButton, { backgroundColor: item.is_completed ? '#FF9800' : '#4CAF50' }]}
                        onPress={() => handleToggleCompleted(item)}
                    >
                        <Ionicons 
                            name={item.is_completed ? "refresh-outline" : "checkmark-outline"} 
                            size={20} 
                            color="white" 
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[styles.actionButton, { backgroundColor: '#666' }]}
                        onPress={() => handleEditItem(item)}
                    >
                        <Ionicons name="pencil-outline" size={20} color="white" />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[styles.actionButton, { backgroundColor: '#dc4446' }]}
                        onPress={() => handleDeleteItem(item.id)}
                    >
                        <Ionicons name="trash-outline" size={20} color="white" />
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );

    return (
        <View style={styles.container}>
            <ScrollView style={styles.scrollView}>
                <TouchableOpacity
                    style={styles.addButton}
                    onPress={() => setShowAddModal(true)}
                >
                    <Ionicons name="add-circle-outline" size={24} color="white" />
                    <Text style={styles.addButtonText}>{i18n.t('plannedPurchases.addItem')}</Text>
                </TouchableOpacity>

                {items.length === 0 ? (
                    <View style={styles.emptyState}>
                        <Ionicons name="list-outline" size={64} color="#ccc" />
                        <Text style={styles.emptyText}>{i18n.t('plannedPurchases.noItems')}</Text>
                        <Text style={styles.emptySubtext}>{i18n.t('plannedPurchases.addFirstItem')}</Text>
                    </View>
                ) : (
                    <View style={styles.itemsList}>
                        {items.map(renderItem)}
                    </View>
                )}
            </ScrollView>

            {/* Add/Edit Modal */}
            <Modal
                visible={showAddModal}
                transparent={true}
                animationType="slide"
                onRequestClose={() => {
                    resetForm();
                    setShowAddModal(false);
                }}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContent}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>
                                {editingItem ? i18n.t('plannedPurchases.editItem') : i18n.t('plannedPurchases.addItem')}
                            </Text>
                            <TouchableOpacity
                                onPress={() => {
                                    resetForm();
                                    setShowAddModal(false);
                                }}
                                style={styles.modalCloseButton}
                            >
                                <Ionicons name="close" size={24} color="#666" />
                            </TouchableOpacity>
                        </View>

                        <ScrollView style={styles.modalBody}>
                            <Text style={styles.inputLabel}>{i18n.t('plannedPurchases.itemName')}</Text>
                            <TextInput
                                style={styles.textInput}
                                placeholder={i18n.t('plannedPurchases.itemNameRequired')}
                                value={itemName}
                                onChangeText={setItemName}
                            />

                            <Text style={styles.inputLabel}>{i18n.t('plannedPurchases.targetPrice')}</Text>
                            <TextInput
                                style={styles.textInput}
                                placeholder="0.00"
                                value={targetPrice}
                                onChangeText={setTargetPrice}
                                keyboardType="numeric"
                            />

                            <Text style={styles.inputLabel}>{i18n.t('plannedPurchases.currentPrice')} {i18n.t('plannedPurchases.optional')}</Text>
                            <TextInput
                                style={styles.textInput}
                                placeholder="0.00"
                                value={currentPrice}
                                onChangeText={setCurrentPrice}
                                keyboardType="numeric"
                            />

                            <Text style={styles.inputLabel}>{i18n.t('plannedPurchases.note')} {i18n.t('plannedPurchases.optional')}</Text>
                            <TextInput
                                style={[styles.textInput, styles.noteInput]}
                                placeholder={i18n.t('plannedPurchases.note')}
                                value={note}
                                onChangeText={setNote}
                                multiline
                                numberOfLines={3}
                            />

                            <Text style={styles.inputLabel}>{i18n.t('plannedPurchases.priority')}</Text>
                            <View style={styles.prioritySelector}>
                                {[1, 2, 3].map(p => (
                                    <TouchableOpacity
                                        key={p}
                                        style={[
                                            styles.priorityOption,
                                            priority === p && styles.selectedPriority,
                                            { borderColor: getPriorityColor(p) }
                                        ]}
                                        onPress={() => setPriority(p)}
                                    >
                                        <Text style={[
                                            styles.priorityOptionText,
                                            priority === p && { color: getPriorityColor(p) }
                                        ]}>
                                            {getPriorityText(p)}
                                        </Text>
                                    </TouchableOpacity>
                                ))}
                            </View>
                        </ScrollView>

                        <View style={styles.modalActions}>
                            <TouchableOpacity
                                style={styles.cancelButton}
                                onPress={() => {
                                    resetForm();
                                    setShowAddModal(false);
                                }}
                            >
                                <Text style={styles.cancelButtonText}>{i18n.t('common.cancel')}</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={styles.confirmButton}
                                onPress={handleAddItem}
                            >
                                <Text style={styles.confirmButtonText}>
                                    {editingItem ? i18n.t('common.edit') : i18n.t('common.add')}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    scrollView: {
        flex: 1,
        padding: 16,
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#FF5722',
        paddingVertical: 14,
        paddingHorizontal: 16,
        borderRadius: 12,
        marginBottom: 20,
        shadowColor: '#FF5722',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 3,
    },
    addButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
        marginLeft: 8,
    },
    emptyState: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 18,
        color: '#666',
        marginTop: 16,
        fontWeight: '500',
    },
    emptySubtext: {
        fontSize: 14,
        color: '#999',
        marginTop: 8,
        textAlign: 'center',
    },
    itemsList: {
        gap: 12,
    },
    itemCard: {
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    completedItem: {
        opacity: 0.7,
        backgroundColor: '#f9f9f9',
    },
    itemHeader: {
        flexDirection: 'row',
        alignItems: 'flex-start',
    },
    itemInfo: {
        flex: 1,
        marginRight: 12,
    },
    itemName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 8,
    },
    completedText: {
        textDecorationLine: 'line-through',
        color: '#999',
    },
    priceRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 4,
        flexWrap: 'wrap',
    },
    priceLabel: {
        fontSize: 14,
        color: '#666',
    },
    targetPrice: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
    },
    currentPrice: {
        fontSize: 14,
        fontWeight: '600',
        color: '#FF9800',
    },
    goodPrice: {
        color: '#4CAF50',
    },
    itemNote: {
        fontSize: 14,
        color: '#666',
        marginBottom: 8,
        fontStyle: 'italic',
    },
    itemMeta: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    priorityBadge: {
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 4,
    },
    priorityText: {
        fontSize: 12,
        color: 'white',
        fontWeight: '500',
    },
    statusText: {
        fontSize: 12,
        color: '#666',
    },
    itemActions: {
        flexDirection: 'row',
        gap: 8,
    },
    actionButton: {
        width: 36,
        height: 36,
        borderRadius: 18,
        alignItems: 'center',
        justifyContent: 'center',
    },
    // Modal styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 16,
        width: '90%',
        maxWidth: 400,
        maxHeight: '80%',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
    },
    modalCloseButton: {
        padding: 4,
    },
    modalBody: {
        padding: 20,
        maxHeight: 400,
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: '#333',
        marginBottom: 8,
        marginTop: 12,
    },
    textInput: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
    },
    noteInput: {
        height: 80,
        textAlignVertical: 'top',
    },
    prioritySelector: {
        flexDirection: 'row',
        gap: 8,
        marginTop: 8,
    },
    priorityOption: {
        flex: 1,
        paddingVertical: 8,
        paddingHorizontal: 12,
        borderRadius: 8,
        borderWidth: 2,
        alignItems: 'center',
        backgroundColor: '#f9f9f9',
    },
    selectedPriority: {
        backgroundColor: 'white',
    },
    priorityOptionText: {
        fontSize: 14,
        fontWeight: '500',
        color: '#666',
    },
    modalActions: {
        flexDirection: 'row',
        padding: 20,
        gap: 12,
    },
    cancelButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
        alignItems: 'center',
    },
    cancelButtonText: {
        fontSize: 16,
        color: '#666',
    },
    confirmButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#FF5722',
        alignItems: 'center',
    },
    confirmButtonText: {
        fontSize: 16,
        color: 'white',
        fontWeight: '500',
    },
});

export default PlannedPurchases;
